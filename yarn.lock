# THIS IS AN AUTOGENERATED FILE. DO NOT EDIT THIS FILE DIRECTLY.
# yarn lockfile v1


ansi-regex@^3.0.0:
  version "3.0.1"
  resolved "https://registry.yarnpkg.com/ansi-regex/-/ansi-regex-3.0.1.tgz#123d6479e92ad45ad897d4054e3c7ca7db4944e1"

ansi-styles@^3.2.1:
  version "3.2.1"
  resolved "http://registry.npm.taobao.org/ansi-styles/download/ansi-styles-3.2.1.tgz#41fbb20243e50b12be0f04b8dedbf07520ce841d"
  dependencies:
    color-convert "^1.9.0"

at-least-node@^1.0.0:
  version "1.0.0"
  resolved "https://registry.yarnpkg.com/at-least-node/-/at-least-node-1.0.0.tgz#602cd4b46e844ad4effc92a8011a3c46e0238dc2"

balanced-match@^1.0.0:
  version "1.0.0"
  resolved "http://registry.npm.taobao.org/balanced-match/download/balanced-match-1.0.0.tgz#89b4d199ab2bee49de164ea02b89ce462d71b767"

base64-js@^1.0.2:
  version "1.3.1"
  resolved "https://registry.yarnpkg.com/base64-js/-/base64-js-1.3.1.tgz#58ece8cb75dd07e71ed08c736abc5fac4dbf8df1"

bl@^1.0.0:
  version "1.2.3"
  resolved "https://registry.yarnpkg.com/bl/-/bl-1.2.3.tgz#1e8dd80142eac80d7158c9dccc047fb620e035e7"
  dependencies:
    readable-stream "^2.3.5"
    safe-buffer "^5.1.1"

brace-expansion@^1.1.7:
  version "1.1.11"
  resolved "http://registry.npm.taobao.org/brace-expansion/download/brace-expansion-1.1.11.tgz#3c7fcbf529d87226f3d2f52b966ff5271eb441dd"
  dependencies:
    balanced-match "^1.0.0"
    concat-map "0.0.1"

buffer-alloc-unsafe@^1.1.0:
  version "1.1.0"
  resolved "https://registry.yarnpkg.com/buffer-alloc-unsafe/-/buffer-alloc-unsafe-1.1.0.tgz#bd7dc26ae2972d0eda253be061dba992349c19f0"

buffer-alloc@^1.2.0:
  version "1.2.0"
  resolved "https://registry.yarnpkg.com/buffer-alloc/-/buffer-alloc-1.2.0.tgz#890dd90d923a873e08e10e5fd51a57e5b7cce0ec"
  dependencies:
    buffer-alloc-unsafe "^1.1.0"
    buffer-fill "^1.0.0"

buffer-crc32@~0.2.3:
  version "0.2.13"
  resolved "https://registry.yarnpkg.com/buffer-crc32/-/buffer-crc32-0.2.13.tgz#0d333e3f00eac50aa1454abd30ef8c2a5d9a7242"

buffer-fill@^1.0.0:
  version "1.0.0"
  resolved "https://registry.yarnpkg.com/buffer-fill/-/buffer-fill-1.0.0.tgz#f8f78b76789888ef39f205cd637f68e702122b2c"

buffer@^5.2.1:
  version "5.6.0"
  resolved "https://registry.yarnpkg.com/buffer/-/buffer-5.6.0.tgz#a31749dc7d81d84db08abf937b6b8c4033f62786"
  dependencies:
    base64-js "^1.0.2"
    ieee754 "^1.1.4"

capture-stack-trace@^1.0.0:
  version "1.0.1"
  resolved "http://registry.npm.taobao.org/capture-stack-trace/download/capture-stack-trace-1.0.1.tgz#a6c0bbe1f38f3aa0b92238ecb6ff42c344d4135d"

caw@^2.0.0:
  version "2.0.1"
  resolved "http://registry.npm.taobao.org/caw/download/caw-2.0.1.tgz#6c3ca071fc194720883c2dc5da9b074bfc7e9e95"
  dependencies:
    get-proxy "^2.0.0"
    isurl "^1.0.0-alpha5"
    tunnel-agent "^0.6.0"
    url-to-options "^1.0.1"

chalk@^2.0.1, chalk@^2.3.1:
  version "2.4.1"
  resolved "http://registry.npm.taobao.org/chalk/download/chalk-2.4.1.tgz#18c49ab16a037b6eb0152cc83e3471338215b66e"
  dependencies:
    ansi-styles "^3.2.1"
    escape-string-regexp "^1.0.5"
    supports-color "^5.3.0"

cli-cursor@^2.1.0:
  version "2.1.0"
  resolved "http://registry.npm.taobao.org/cli-cursor/download/cli-cursor-2.1.0.tgz#b35dac376479facc3e94747d41d0d0f5238ffcb5"
  dependencies:
    restore-cursor "^2.0.0"

cli-spinners@^1.1.0:
  version "1.3.1"
  resolved "http://registry.npm.taobao.org/cli-spinners/download/cli-spinners-1.3.1.tgz#002c1990912d0d59580c93bd36c056de99e4259a"

clone@^1.0.2:
  version "1.0.4"
  resolved "http://registry.npm.taobao.org/clone/download/clone-1.0.4.tgz#da309cc263df15994c688ca902179ca3c7cd7c7e"

color-convert@^1.9.0:
  version "1.9.3"
  resolved "http://registry.npm.taobao.org/color-convert/download/color-convert-1.9.3.tgz#bb71850690e1f136567de629d2d5471deda4c1e8"
  dependencies:
    color-name "1.1.3"

color-name@1.1.3:
  version "1.1.3"
  resolved "http://registry.npm.taobao.org/color-name/download/color-name-1.1.3.tgz#a7d0558bd89c42f795dd42328f740831ca53bc25"

commander@^2.8.1:
  version "2.20.3"
  resolved "https://registry.yarnpkg.com/commander/-/commander-2.20.3.tgz#fd485e84c03eb4881c20722ba48035e8531aeb33"

concat-map@0.0.1:
  version "0.0.1"
  resolved "http://registry.npm.taobao.org/concat-map/download/concat-map-0.0.1.tgz#d8a96bd77fd68df7793a73036a3ba0d5405d477b"

config-chain@^1.1.11:
  version "1.1.12"
  resolved "http://registry.npm.taobao.org/config-chain/download/config-chain-1.1.12.tgz#0fde8d091200eb5e808caf25fe618c02f48e4efa"
  dependencies:
    ini "^1.3.4"
    proto-list "~1.2.1"

core-util-is@~1.0.0:
  version "1.0.2"
  resolved "https://registry.yarnpkg.com/core-util-is/-/core-util-is-1.0.2.tgz#b5fd54220aa2bc5ab57aab7140c940754503c1a7"

create-error-class@^3.0.0:
  version "3.0.2"
  resolved "http://registry.npm.taobao.org/create-error-class/download/create-error-class-3.0.2.tgz#06be7abef947a3f14a30fd610671d401bca8b7b6"
  dependencies:
    capture-stack-trace "^1.0.0"

decompress-tar@^4.0.0, decompress-tar@^4.1.0, decompress-tar@^4.1.1:
  version "4.1.1"
  resolved "https://registry.yarnpkg.com/decompress-tar/-/decompress-tar-4.1.1.tgz#718cbd3fcb16209716e70a26b84e7ba4592e5af1"
  dependencies:
    file-type "^5.2.0"
    is-stream "^1.1.0"
    tar-stream "^1.5.2"

decompress-tarbz2@^4.0.0:
  version "4.1.1"
  resolved "https://registry.yarnpkg.com/decompress-tarbz2/-/decompress-tarbz2-4.1.1.tgz#3082a5b880ea4043816349f378b56c516be1a39b"
  dependencies:
    decompress-tar "^4.1.0"
    file-type "^6.1.0"
    is-stream "^1.1.0"
    seek-bzip "^1.0.5"
    unbzip2-stream "^1.0.9"

decompress-targz@^4.0.0:
  version "4.1.1"
  resolved "https://registry.yarnpkg.com/decompress-targz/-/decompress-targz-4.1.1.tgz#c09bc35c4d11f3de09f2d2da53e9de23e7ce1eee"
  dependencies:
    decompress-tar "^4.1.1"
    file-type "^5.2.0"
    is-stream "^1.1.0"

decompress-unzip@^4.0.1:
  version "4.0.1"
  resolved "https://registry.yarnpkg.com/decompress-unzip/-/decompress-unzip-4.0.1.tgz#deaaccdfd14aeaf85578f733ae8210f9b4848f69"
  dependencies:
    file-type "^3.8.0"
    get-stream "^2.2.0"
    pify "^2.3.0"
    yauzl "^2.4.2"

decompress@^4.0.0:
  version "4.2.1"
  resolved "https://registry.yarnpkg.com/decompress/-/decompress-4.2.1.tgz#007f55cc6a62c055afa37c07eb6a4ee1b773f118"
  dependencies:
    decompress-tar "^4.0.0"
    decompress-tarbz2 "^4.0.0"
    decompress-targz "^4.0.0"
    decompress-unzip "^4.0.1"
    graceful-fs "^4.1.10"
    make-dir "^1.0.0"
    pify "^2.3.0"
    strip-dirs "^2.0.0"

defaults@^1.0.3:
  version "1.0.3"
  resolved "http://registry.npm.taobao.org/defaults/download/defaults-1.0.3.tgz#c656051e9817d9ff08ed881477f3fe4019f3ef7d"
  dependencies:
    clone "^1.0.2"

download-git-repo@^1.1.0:
  version "1.1.0"
  resolved "http://registry.npm.taobao.org/download-git-repo/download/download-git-repo-1.1.0.tgz#7dc88a82ced064b1372a0002f8a3aebf10eb1d3c"
  dependencies:
    download "^5.0.3"
    git-clone "^0.1.0"
    rimraf "^2.6.1"

download@^5.0.3:
  version "5.0.3"
  resolved "http://registry.npm.taobao.org/download/download/download-5.0.3.tgz#63537f977f99266a30eb8a2a2fbd1f20b8000f7a"
  dependencies:
    caw "^2.0.0"
    decompress "^4.0.0"
    filenamify "^2.0.0"
    get-stream "^3.0.0"
    got "^6.3.0"
    mkdirp "^0.5.1"
    pify "^2.3.0"

duplexer3@^0.1.4:
  version "0.1.4"
  resolved "http://registry.npm.taobao.org/duplexer3/download/duplexer3-0.1.4.tgz#ee01dd1cac0ed3cbc7fdbea37dc0a8f1ce002ce2"

end-of-stream@^1.0.0:
  version "1.4.4"
  resolved "https://registry.yarnpkg.com/end-of-stream/-/end-of-stream-1.4.4.tgz#5ae64a5f45057baf3626ec14da0ca5e4b2431eb0"
  dependencies:
    once "^1.4.0"

escape-string-regexp@^1.0.2, escape-string-regexp@^1.0.5:
  version "1.0.5"
  resolved "http://registry.npm.taobao.org/escape-string-regexp/download/escape-string-regexp-1.0.5.tgz#1b61c0562190a8dff6ae3bb2cf0200ca130b86d4"

fd-slicer@~1.1.0:
  version "1.1.0"
  resolved "https://registry.yarnpkg.com/fd-slicer/-/fd-slicer-1.1.0.tgz#25c7c89cb1f9077f8891bbe61d8f390eae256f1e"
  dependencies:
    pend "~1.2.0"

file-type@^3.8.0:
  version "3.9.0"
  resolved "https://registry.yarnpkg.com/file-type/-/file-type-3.9.0.tgz#257a078384d1db8087bc449d107d52a52672b9e9"

file-type@^5.2.0:
  version "5.2.0"
  resolved "https://registry.yarnpkg.com/file-type/-/file-type-5.2.0.tgz#2ddbea7c73ffe36368dfae49dc338c058c2b8ad6"

file-type@^6.1.0:
  version "6.2.0"
  resolved "https://registry.yarnpkg.com/file-type/-/file-type-6.2.0.tgz#e50cd75d356ffed4e306dc4f5bcf52a79903a919"

filename-reserved-regex@^2.0.0:
  version "2.0.0"
  resolved "http://registry.npm.taobao.org/filename-reserved-regex/download/filename-reserved-regex-2.0.0.tgz#abf73dfab735d045440abfea2d91f389ebbfa229"

filenamify@^2.0.0:
  version "2.1.0"
  resolved "http://registry.npm.taobao.org/filenamify/download/filenamify-2.1.0.tgz#88faf495fb1b47abfd612300002a16228c677ee9"
  dependencies:
    filename-reserved-regex "^2.0.0"
    strip-outer "^1.0.0"
    trim-repeated "^1.0.0"

fs-constants@^1.0.0:
  version "1.0.0"
  resolved "https://registry.yarnpkg.com/fs-constants/-/fs-constants-1.0.0.tgz#6be0de9be998ce16af8afc24497b9ee9b7ccd9ad"

fs-extra@^9.0.1:
  version "9.0.1"
  resolved "https://registry.yarnpkg.com/fs-extra/-/fs-extra-9.0.1.tgz#910da0062437ba4c39fedd863f1675ccfefcb9fc"
  dependencies:
    at-least-node "^1.0.0"
    graceful-fs "^4.2.0"
    jsonfile "^6.0.1"
    universalify "^1.0.0"

fs.realpath@^1.0.0:
  version "1.0.0"
  resolved "http://registry.npm.taobao.org/fs.realpath/download/fs.realpath-1.0.0.tgz#1504ad2523158caa40db4a2787cb01411994ea4f"

get-proxy@^2.0.0:
  version "2.1.0"
  resolved "http://registry.npm.taobao.org/get-proxy/download/get-proxy-2.1.0.tgz#349f2b4d91d44c4d4d4e9cba2ad90143fac5ef93"
  dependencies:
    npm-conf "^1.1.0"

get-stream@^2.2.0:
  version "2.3.1"
  resolved "https://registry.yarnpkg.com/get-stream/-/get-stream-2.3.1.tgz#5f38f93f346009666ee0150a054167f91bdd95de"
  dependencies:
    object-assign "^4.0.1"
    pinkie-promise "^2.0.0"

get-stream@^3.0.0:
  version "3.0.0"
  resolved "http://registry.npm.taobao.org/get-stream/download/get-stream-3.0.0.tgz#8e943d1358dc37555054ecbe2edb05aa174ede14"

git-clone@^0.1.0:
  version "0.1.0"
  resolved "http://registry.npm.taobao.org/git-clone/download/git-clone-0.1.0.tgz#0d76163778093aef7f1c30238f2a9ef3f07a2eb9"

glob@^7.0.5, glob@^7.1.3:
  version "7.1.3"
  resolved "http://registry.npm.taobao.org/glob/download/glob-7.1.3.tgz#3960832d3f1574108342dafd3a67b332c0969df1"
  dependencies:
    fs.realpath "^1.0.0"
    inflight "^1.0.4"
    inherits "2"
    minimatch "^3.0.4"
    once "^1.3.0"
    path-is-absolute "^1.0.0"

got@^6.3.0:
  version "6.7.1"
  resolved "http://registry.npm.taobao.org/got/download/got-6.7.1.tgz#240cd05785a9a18e561dc1b44b41c763ef1e8db0"
  dependencies:
    create-error-class "^3.0.0"
    duplexer3 "^0.1.4"
    get-stream "^3.0.0"
    is-redirect "^1.0.0"
    is-retry-allowed "^1.0.0"
    is-stream "^1.0.0"
    lowercase-keys "^1.0.0"
    safe-buffer "^5.0.1"
    timed-out "^4.0.0"
    unzip-response "^2.0.1"
    url-parse-lax "^1.0.0"

graceful-fs@^4.1.10, graceful-fs@^4.1.6, graceful-fs@^4.2.0:
  version "4.2.4"
  resolved "https://registry.yarnpkg.com/graceful-fs/-/graceful-fs-4.2.4.tgz#2256bde14d3632958c465ebc96dc467ca07a29fb"

has-flag@^3.0.0:
  version "3.0.0"
  resolved "http://registry.npm.taobao.org/has-flag/download/has-flag-3.0.0.tgz#b5d454dc2199ae225699f3467e5a07f3b955bafd"

has-symbol-support-x@^1.4.1:
  version "1.4.2"
  resolved "http://registry.npm.taobao.org/has-symbol-support-x/download/has-symbol-support-x-1.4.2.tgz#1409f98bc00247da45da67cee0a36f282ff26455"

has-to-string-tag-x@^1.2.0:
  version "1.4.1"
  resolved "http://registry.npm.taobao.org/has-to-string-tag-x/download/has-to-string-tag-x-1.4.1.tgz#a045ab383d7b4b2012a00148ab0aa5f290044d4d"
  dependencies:
    has-symbol-support-x "^1.4.1"

ieee754@^1.1.4:
  version "1.1.13"
  resolved "https://registry.yarnpkg.com/ieee754/-/ieee754-1.1.13.tgz#ec168558e95aa181fd87d37f55c32bbcb6708b84"

inflight@^1.0.4:
  version "1.0.6"
  resolved "http://registry.npm.taobao.org/inflight/download/inflight-1.0.6.tgz#49bd6331d7d02d0c09bc910a1075ba8165b56df9"
  dependencies:
    once "^1.3.0"
    wrappy "1"

inherits@2:
  version "2.0.3"
  resolved "http://registry.npm.taobao.org/inherits/download/inherits-2.0.3.tgz#633c2c83e3da42a502f52466022480f4208261de"

inherits@~2.0.3:
  version "2.0.4"
  resolved "https://registry.yarnpkg.com/inherits/-/inherits-2.0.4.tgz#0fa2c64f932917c3433a0ded55363aae37416b7c"

ini@^1.3.4:
  version "1.3.8"
  resolved "https://registry.yarnpkg.com/ini/-/ini-1.3.8.tgz#a29da425b48806f34767a4efce397269af28432c"

is-natural-number@^4.0.1:
  version "4.0.1"
  resolved "https://registry.yarnpkg.com/is-natural-number/-/is-natural-number-4.0.1.tgz#ab9d76e1db4ced51e35de0c72ebecf09f734cde8"

is-object@^1.0.1:
  version "1.0.1"
  resolved "http://registry.npm.taobao.org/is-object/download/is-object-1.0.1.tgz#8952688c5ec2ffd6b03ecc85e769e02903083470"

is-redirect@^1.0.0:
  version "1.0.0"
  resolved "http://registry.npm.taobao.org/is-redirect/download/is-redirect-1.0.0.tgz#1d03dded53bd8db0f30c26e4f95d36fc7c87dc24"

is-retry-allowed@^1.0.0:
  version "1.1.0"
  resolved "http://registry.npm.taobao.org/is-retry-allowed/download/is-retry-allowed-1.1.0.tgz#11a060568b67339444033d0125a61a20d564fb34"

is-stream@^1.0.0, is-stream@^1.1.0:
  version "1.1.0"
  resolved "http://registry.npm.taobao.org/is-stream/download/is-stream-1.1.0.tgz#12d4a3dd4e68e0b79ceb8dbc84173ae80d91ca44"

isarray@~1.0.0:
  version "1.0.0"
  resolved "https://registry.yarnpkg.com/isarray/-/isarray-1.0.0.tgz#bb935d48582cba168c06834957a54a3e07124f11"

isbinaryfile@^3.0.3:
  version "3.0.3"
  resolved "http://registry.npm.taobao.org/isbinaryfile/download/isbinaryfile-3.0.3.tgz#5d6def3edebf6e8ca8cae9c30183a804b5f8be80"
  dependencies:
    buffer-alloc "^1.2.0"

isurl@^1.0.0-alpha5:
  version "1.0.0"
  resolved "http://registry.npm.taobao.org/isurl/download/isurl-1.0.0.tgz#b27f4f49f3cdaa3ea44a0a5b7f3462e6edc39d67"
  dependencies:
    has-to-string-tag-x "^1.2.0"
    is-object "^1.0.1"

jsonfile@^6.0.1:
  version "6.1.0"
  resolved "https://registry.yarnpkg.com/jsonfile/-/jsonfile-6.1.0.tgz#bc55b2634793c679ec6403094eb13698a6ec0aae"
  dependencies:
    universalify "^2.0.0"
  optionalDependencies:
    graceful-fs "^4.1.6"

log-symbols@^2.2.0:
  version "2.2.0"
  resolved "http://registry.npm.taobao.org/log-symbols/download/log-symbols-2.2.0.tgz#5740e1c5d6f0dfda4ad9323b5332107ef6b4c40a"
  dependencies:
    chalk "^2.0.1"

lowercase-keys@^1.0.0:
  version "1.0.1"
  resolved "http://registry.npm.taobao.org/lowercase-keys/download/lowercase-keys-1.0.1.tgz#6f9e30b47084d971a7c820ff15a6c5167b74c26f"

make-dir@^1.0.0:
  version "1.3.0"
  resolved "https://registry.yarnpkg.com/make-dir/-/make-dir-1.3.0.tgz#79c1033b80515bd6d24ec9933e860ca75ee27f0c"
  dependencies:
    pify "^3.0.0"

mimic-fn@^1.0.0:
  version "1.2.0"
  resolved "http://registry.npm.taobao.org/mimic-fn/download/mimic-fn-1.2.0.tgz#820c86a39334640e99516928bd03fca88057d022"

minimatch@^3.0.4:
  version "3.0.4"
  resolved "http://registry.npm.taobao.org/minimatch/download/minimatch-3.0.4.tgz#5166e286457f03306064be5497e8dbb0c3d32083"
  dependencies:
    brace-expansion "^1.1.7"

minimist@0.0.8:
  version "0.0.8"
  resolved "http://registry.npm.taobao.org/minimist/download/minimist-0.0.8.tgz#857fcabfc3397d2625b8228262e86aa7a011b05d"

mkdirp@^0.5.1:
  version "0.5.1"
  resolved "http://registry.npm.taobao.org/mkdirp/download/mkdirp-0.5.1.tgz#30057438eac6cf7f8c4767f38648d6697d75c903"
  dependencies:
    minimist "0.0.8"

npm-conf@^1.1.0:
  version "1.1.3"
  resolved "http://registry.npm.taobao.org/npm-conf/download/npm-conf-1.1.3.tgz#256cc47bd0e218c259c4e9550bf413bc2192aff9"
  dependencies:
    config-chain "^1.1.11"
    pify "^3.0.0"

object-assign@^4.0.1:
  version "4.1.1"
  resolved "https://registry.yarnpkg.com/object-assign/-/object-assign-4.1.1.tgz#2109adc7965887cfc05cbbd442cac8bfbb360863"

once@^1.3.0, once@^1.4.0:
  version "1.4.0"
  resolved "http://registry.npm.taobao.org/once/download/once-1.4.0.tgz#583b1aa775961d4b113ac17d9c50baef9dd76bd1"
  dependencies:
    wrappy "1"

onetime@^2.0.0:
  version "2.0.1"
  resolved "http://registry.npm.taobao.org/onetime/download/onetime-2.0.1.tgz#067428230fd67443b2794b22bba528b6867962d4"
  dependencies:
    mimic-fn "^1.0.0"

ora@^3.0.0:
  version "3.0.0"
  resolved "http://registry.npm.taobao.org/ora/download/ora-3.0.0.tgz#8179e3525b9aafd99242d63cc206fd64732741d0"
  dependencies:
    chalk "^2.3.1"
    cli-cursor "^2.1.0"
    cli-spinners "^1.1.0"
    log-symbols "^2.2.0"
    strip-ansi "^4.0.0"
    wcwidth "^1.0.1"

os-homedir@^1.0.0:
  version "1.0.2"
  resolved "http://registry.npm.taobao.org/os-homedir/download/os-homedir-1.0.2.tgz#ffbc4988336e0e833de0c168c7ef152121aa7fb3"

path-is-absolute@^1.0.0:
  version "1.0.1"
  resolved "http://registry.npm.taobao.org/path-is-absolute/download/path-is-absolute-1.0.1.tgz#174b9268735534ffbc7ace6bf53a5a9e1b5c5f5f"

pend@~1.2.0:
  version "1.2.0"
  resolved "https://registry.yarnpkg.com/pend/-/pend-1.2.0.tgz#7a57eb550a6783f9115331fcf4663d5c8e007a50"

pify@^2.3.0:
  version "2.3.0"
  resolved "https://registry.yarnpkg.com/pify/-/pify-2.3.0.tgz#ed141a6ac043a849ea588498e7dca8b15330e90c"

pify@^3.0.0:
  version "3.0.0"
  resolved "https://registry.yarnpkg.com/pify/-/pify-3.0.0.tgz#e5a4acd2c101fdf3d9a4d07f0dbc4db49dd28176"

pinkie-promise@^2.0.0:
  version "2.0.1"
  resolved "https://registry.yarnpkg.com/pinkie-promise/-/pinkie-promise-2.0.1.tgz#2135d6dfa7a358c069ac9b178776288228450ffa"
  dependencies:
    pinkie "^2.0.0"

pinkie@^2.0.0:
  version "2.0.4"
  resolved "https://registry.yarnpkg.com/pinkie/-/pinkie-2.0.4.tgz#72556b80cfa0d48a974e80e77248e80ed4f7f870"

prepend-http@^1.0.1:
  version "1.0.4"
  resolved "http://registry.npm.taobao.org/prepend-http/download/prepend-http-1.0.4.tgz#d4f4562b0ce3696e41ac52d0e002e57a635dc6dc"

process-nextick-args@~2.0.0:
  version "2.0.1"
  resolved "https://registry.yarnpkg.com/process-nextick-args/-/process-nextick-args-2.0.1.tgz#7820d9b16120cc55ca9ae7792680ae7dba6d7fe2"

proto-list@~1.2.1:
  version "1.2.4"
  resolved "http://registry.npm.taobao.org/proto-list/download/proto-list-1.2.4.tgz#212d5bfe1318306a420f6402b8e26ff39647a849"

readable-stream@^2.3.0, readable-stream@^2.3.5:
  version "2.3.7"
  resolved "https://registry.yarnpkg.com/readable-stream/-/readable-stream-2.3.7.tgz#1eca1cf711aef814c04f62252a36a62f6cb23b57"
  dependencies:
    core-util-is "~1.0.0"
    inherits "~2.0.3"
    isarray "~1.0.0"
    process-nextick-args "~2.0.0"
    safe-buffer "~5.1.1"
    string_decoder "~1.1.1"
    util-deprecate "~1.0.1"

restore-cursor@^2.0.0:
  version "2.0.0"
  resolved "http://registry.npm.taobao.org/restore-cursor/download/restore-cursor-2.0.0.tgz#9f7ee287f82fd326d4fd162923d62129eee0dfaf"
  dependencies:
    onetime "^2.0.0"
    signal-exit "^3.0.2"

rimraf@^2.6.1, rimraf@^2.6.2:
  version "2.6.2"
  resolved "http://registry.npm.taobao.org/rimraf/download/rimraf-2.6.2.tgz#2ed8150d24a16ea8651e6d6ef0f47c4158ce7a36"
  dependencies:
    glob "^7.0.5"

safe-buffer@^5.0.1, safe-buffer@~5.1.0, safe-buffer@~5.1.1:
  version "5.1.2"
  resolved "http://registry.npm.taobao.org/safe-buffer/download/safe-buffer-5.1.2.tgz#991ec69d296e0313747d59bdfd2b745c35f8828d"

safe-buffer@^5.1.1:
  version "5.2.1"
  resolved "https://registry.yarnpkg.com/safe-buffer/-/safe-buffer-5.2.1.tgz#1eaf9fa9bdb1fdd4ec75f58f9cdb4e6b7827eec6"

seek-bzip@^1.0.5:
  version "1.0.6"
  resolved "https://registry.yarnpkg.com/seek-bzip/-/seek-bzip-1.0.6.tgz#35c4171f55a680916b52a07859ecf3b5857f21c4"
  dependencies:
    commander "^2.8.1"

signal-exit@^3.0.2:
  version "3.0.2"
  resolved "http://registry.npm.taobao.org/signal-exit/download/signal-exit-3.0.2.tgz#b5fdc08f1287ea1178628e415e25132b73646c6d"

string_decoder@~1.1.1:
  version "1.1.1"
  resolved "https://registry.yarnpkg.com/string_decoder/-/string_decoder-1.1.1.tgz#9cf1611ba62685d7030ae9e4ba34149c3af03fc8"
  dependencies:
    safe-buffer "~5.1.0"

strip-ansi@^4.0.0:
  version "4.0.0"
  resolved "http://registry.npm.taobao.org/strip-ansi/download/strip-ansi-4.0.0.tgz#a8479022eb1ac368a871389b635262c505ee368f"
  dependencies:
    ansi-regex "^3.0.0"

strip-dirs@^2.0.0:
  version "2.1.0"
  resolved "https://registry.yarnpkg.com/strip-dirs/-/strip-dirs-2.1.0.tgz#4987736264fc344cf20f6c34aca9d13d1d4ed6c5"
  dependencies:
    is-natural-number "^4.0.1"

strip-json-comments@^2.0.1:
  version "2.0.1"
  resolved "http://registry.npm.taobao.org/strip-json-comments/download/strip-json-comments-2.0.1.tgz#3c531942e908c2697c0ec344858c286c7ca0a60a"

strip-outer@^1.0.0:
  version "1.0.1"
  resolved "http://registry.npm.taobao.org/strip-outer/download/strip-outer-1.0.1.tgz#b2fd2abf6604b9d1e6013057195df836b8a9d631"
  dependencies:
    escape-string-regexp "^1.0.2"

supports-color@^5.3.0:
  version "5.5.0"
  resolved "http://registry.npm.taobao.org/supports-color/download/supports-color-5.5.0.tgz#e2e69a44ac8772f78a1ec0b35b689df6530efc8f"
  dependencies:
    has-flag "^3.0.0"

tar-stream@^1.5.2:
  version "1.6.2"
  resolved "https://registry.yarnpkg.com/tar-stream/-/tar-stream-1.6.2.tgz#8ea55dab37972253d9a9af90fdcd559ae435c555"
  dependencies:
    bl "^1.0.0"
    buffer-alloc "^1.2.0"
    end-of-stream "^1.0.0"
    fs-constants "^1.0.0"
    readable-stream "^2.3.0"
    to-buffer "^1.1.1"
    xtend "^4.0.0"

through@^2.3.8:
  version "2.3.8"
  resolved "https://registry.yarnpkg.com/through/-/through-2.3.8.tgz#0dd4c9ffaabc357960b1b724115d7e0e86a2e1f5"

timed-out@^4.0.0:
  version "4.0.1"
  resolved "http://registry.npm.taobao.org/timed-out/download/timed-out-4.0.1.tgz#f32eacac5a175bea25d7fab565ab3ed8741ef56f"

to-buffer@^1.1.1:
  version "1.1.1"
  resolved "https://registry.yarnpkg.com/to-buffer/-/to-buffer-1.1.1.tgz#493bd48f62d7c43fcded313a03dcadb2e1213a80"

trim-repeated@^1.0.0:
  version "1.0.0"
  resolved "http://registry.npm.taobao.org/trim-repeated/download/trim-repeated-1.0.0.tgz#e3646a2ea4e891312bf7eace6cfb05380bc01c21"
  dependencies:
    escape-string-regexp "^1.0.2"

tunnel-agent@^0.6.0:
  version "0.6.0"
  resolved "http://registry.npm.taobao.org/tunnel-agent/download/tunnel-agent-0.6.0.tgz#27a5dea06b36b04a0a9966774b290868f0fc40fd"
  dependencies:
    safe-buffer "^5.0.1"

unbzip2-stream@^1.0.9:
  version "1.4.3"
  resolved "https://registry.yarnpkg.com/unbzip2-stream/-/unbzip2-stream-1.4.3.tgz#b0da04c4371311df771cdc215e87f2130991ace7"
  dependencies:
    buffer "^5.2.1"
    through "^2.3.8"

universalify@^1.0.0:
  version "1.0.0"
  resolved "https://registry.yarnpkg.com/universalify/-/universalify-1.0.0.tgz#b61a1da173e8435b2fe3c67d29b9adf8594bd16d"

universalify@^2.0.0:
  version "2.0.0"
  resolved "https://registry.yarnpkg.com/universalify/-/universalify-2.0.0.tgz#75a4984efedc4b08975c5aeb73f530d02df25717"

unzip-response@^2.0.1:
  version "2.0.1"
  resolved "http://registry.npm.taobao.org/unzip-response/download/unzip-response-2.0.1.tgz#d2f0f737d16b0615e72a6935ed04214572d56f97"

url-parse-lax@^1.0.0:
  version "1.0.0"
  resolved "http://registry.npm.taobao.org/url-parse-lax/download/url-parse-lax-1.0.0.tgz#7af8f303645e9bd79a272e7a14ac68bc0609da73"
  dependencies:
    prepend-http "^1.0.1"

url-to-options@^1.0.1:
  version "1.0.1"
  resolved "http://registry.npm.taobao.org/url-to-options/download/url-to-options-1.0.1.tgz#1505a03a289a48cbd7a434efbaeec5055f5633a9"

user-home@^2.0.0:
  version "2.0.0"
  resolved "http://registry.npm.taobao.org/user-home/download/user-home-2.0.0.tgz#9c70bfd8169bc1dcbf48604e0f04b8b49cde9e9f"
  dependencies:
    os-homedir "^1.0.0"

util-deprecate@~1.0.1:
  version "1.0.2"
  resolved "https://registry.yarnpkg.com/util-deprecate/-/util-deprecate-1.0.2.tgz#450d4dc9fa70de732762fbd2d4a28981419a0ccf"

wcwidth@^1.0.1:
  version "1.0.1"
  resolved "http://registry.npm.taobao.org/wcwidth/download/wcwidth-1.0.1.tgz#f0b0dcf915bc5ff1528afadb2c0e17b532da2fe8"
  dependencies:
    defaults "^1.0.3"

wrappy@1:
  version "1.0.2"
  resolved "https://registry.yarnpkg.com/wrappy/-/wrappy-1.0.2.tgz#b5243d8f3ec1aa35f1364605bc0d1036e30ab69f"

xtend@^4.0.0:
  version "4.0.2"
  resolved "https://registry.yarnpkg.com/xtend/-/xtend-4.0.2.tgz#bb72779f5fa465186b1f438f674fa347fdb5db54"

yauzl@^2.4.2:
  version "2.10.0"
  resolved "https://registry.yarnpkg.com/yauzl/-/yauzl-2.10.0.tgz#c7eb17c93e112cb1086fa6d8e51fb0667b79a5f9"
  dependencies:
    buffer-crc32 "~0.2.3"
    fd-slicer "~1.1.0"
