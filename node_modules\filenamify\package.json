{"name": "filenamify", "version": "2.1.0", "description": "Convert a string to a valid safe filename", "license": "MIT", "repository": "sindresorhus/filenamify", "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "sindresorhus.com"}, "engines": {"node": ">=4"}, "scripts": {"test": "xo && ava"}, "files": ["index.js"], "keywords": ["filename", "safe", "sanitize", "file", "name", "string", "path", "filepath", "convert", "valid", "dirname"], "dependencies": {"filename-reserved-regex": "^2.0.0", "strip-outer": "^1.0.0", "trim-repeated": "^1.0.0"}, "devDependencies": {"ava": "*", "xo": "*"}}