{"name": "defaults", "version": "1.0.3", "description": "merge single level defaults over a config object", "main": "index.js", "scripts": {"test": "node test.js"}, "repository": {"type": "git", "url": "git://github.com/tmpvar/defaults.git"}, "keywords": ["config", "defaults"], "author": "<PERSON> <<EMAIL>>", "license": "MIT", "readmeFilename": "README.md", "dependencies": {"clone": "^1.0.2"}, "devDependencies": {"tap": "^2.0.0"}}