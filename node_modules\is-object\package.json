{"name": "is-object", "version": "1.0.1", "description": "Checks whether a value is an object", "keywords": [], "author": "Raynos <<EMAIL>>", "repository": "git://github.com/ljharb/is-object.git", "main": "index", "homepage": "https://github.com/ljharb/is-object", "contributors": [{"name": "<PERSON><PERSON>"}, {"name": "<PERSON>", "url": "https://github.com/ljharb"}], "bugs": {"url": "https://github.com/ljharb/is-object/issues", "email": "<EMAIL>"}, "dependencies": {}, "devDependencies": {"tape": "~2.14.0", "covert": "~1.0.0", "jscs": "~1.6.0"}, "license": "MIT", "licenses": [{"type": "MIT", "url": "http://github.com/ljharb/is-object/raw/master/LICENSE"}], "scripts": {"test": "npm run lint && node test/index.js && npm run coverage-quiet", "coverage": "covert test/index.js", "coverage-quiet": "covert test/index.js --quiet", "lint": "jscs *.js */*.js"}, "testling": {"files": "test/index.js", "browsers": ["ie/6..latest", "firefox/3..6", "firefox/16..latest", "firefox/nightly", "chrome/22..latest", "chrome/canary", "opera/10.0", "opera/11..latest", "opera/next", "safari/4..latest", "ipad/6.0..latest", "iphone/6.0..latest"]}}