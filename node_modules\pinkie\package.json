{"name": "pinkie", "version": "2.0.4", "description": "Itty bitty little widdle twinkie pinkie ES2015 Promise implementation", "license": "MIT", "repository": "floatdrop/pinkie", "author": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>", "url": "github.com/floatdrop"}, "engines": {"node": ">=0.10.0"}, "scripts": {"test": "xo && nyc mocha", "coverage": "nyc report --reporter=text-lcov | coveralls"}, "files": ["index.js"], "keywords": ["promise", "promises", "es2015", "es6"], "devDependencies": {"core-assert": "^0.1.1", "coveralls": "^2.11.4", "mocha": "*", "nyc": "^3.2.2", "promises-aplus-tests": "*", "xo": "^0.10.1"}}