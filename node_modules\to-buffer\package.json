{"name": "to-buffer", "version": "1.1.1", "description": "Pass in a string, get a buffer back. Pass in a buffer, get the same buffer back", "main": "index.js", "dependencies": {}, "devDependencies": {"standard": "^6.0.5", "tape": "^4.4.0"}, "scripts": {"test": "standard && tape test.js"}, "repository": {"type": "git", "url": "https://github.com/mafintosh/to-buffer.git"}, "author": "<PERSON> (@mafintosh)", "license": "MIT", "bugs": {"url": "https://github.com/mafintosh/to-buffer/issues"}, "homepage": "https://github.com/mafintosh/to-buffer"}