{"name": "pify", "version": "3.0.0", "description": "Promisify a callback-style function", "license": "MIT", "repository": "sindresorhus/pify", "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "sindresorhus.com"}, "engines": {"node": ">=4"}, "scripts": {"test": "xo && ava && npm run optimization-test", "optimization-test": "node --allow-natives-syntax optimization-test.js"}, "files": ["index.js"], "keywords": ["promise", "promises", "promisify", "all", "denodify", "denodeify", "callback", "cb", "node", "then", "thenify", "convert", "transform", "wrap", "wrapper", "bind", "to", "async", "await", "es2015", "bluebird"], "devDependencies": {"ava": "*", "pinkie-promise": "^2.0.0", "v8-natives": "^1.0.0", "xo": "*"}}