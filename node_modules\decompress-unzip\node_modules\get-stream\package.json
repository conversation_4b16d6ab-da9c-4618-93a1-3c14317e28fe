{"name": "get-stream", "version": "2.3.1", "description": "Get a stream as a string, buffer, or array", "license": "MIT", "repository": "sindresorhus/get-stream", "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "sindresorhus.com"}, "engines": {"node": ">=0.10.0"}, "scripts": {"test": "xo && ava"}, "files": ["index.js", "buffer-stream.js"], "keywords": ["get", "stream", "promise", "concat", "string", "str", "text", "buffer", "read", "data", "readable", "readablestream", "array", "object", "obj"], "dependencies": {"object-assign": "^4.0.1", "pinkie-promise": "^2.0.0"}, "devDependencies": {"ava": "*", "buffer-equals": "^1.0.3", "into-stream": "^2.0.1", "xo": "*"}}