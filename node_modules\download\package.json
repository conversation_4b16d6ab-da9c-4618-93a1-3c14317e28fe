{"name": "download", "version": "5.0.3", "description": "Download and extract files", "license": "MIT", "repository": "kevva/download", "author": {"email": "kevin<PERSON><PERSON><PERSON>@gmail.com", "name": "<PERSON>", "url": "github.com/kevva"}, "engines": {"node": ">=4"}, "scripts": {"test": "xo && ava"}, "files": ["index.js"], "keywords": ["download", "extract", "http", "request", "url"], "dependencies": {"caw": "^2.0.0", "decompress": "^4.0.0", "filenamify": "^2.0.0", "get-stream": "^3.0.0", "got": "^6.3.0", "mkdirp": "^0.5.1", "pify": "^2.3.0"}, "devDependencies": {"ava": "*", "is-zip": "^1.0.0", "nock": "^9.0.2", "path-exists": "^3.0.0", "random-buffer": "^0.1.0", "rimraf": "^2.2.8", "xo": "*"}, "xo": {"esnext": true}}