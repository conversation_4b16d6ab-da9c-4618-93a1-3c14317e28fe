'use strict';
const zlib = require('zlib');
const decompressTar = require('decompress-tar');
const fileType = require('file-type');
const isStream = require('is-stream');

module.exports = () => input => {
	if (!Buffer.isBuffer(input) && !isStream(input)) {
		return Promise.reject(new TypeError(`Expected a Buffer or Stream, got ${typeof input}`));
	}

	if (Buffer.isBuffer(input) && (!fileType(input) || fileType(input).ext !== 'gz')) {
		return Promise.resolve([]);
	}

	const unzip = zlib.createGunzip();
	const result = decompressTar()(unzip);

	if (Buffer.isBuffer(input)) {
		unzip.end(input);
	} else {
		input.pipe(unzip);
	}

	return result;
};
