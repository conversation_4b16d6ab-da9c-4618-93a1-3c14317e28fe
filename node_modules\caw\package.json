{"name": "caw", "version": "2.0.1", "description": "Construct HTTP/HTTPS agents for tunneling proxies", "license": "MIT", "repository": "kevva/caw", "author": {"email": "kevin<PERSON><PERSON><PERSON>@gmail.com", "name": "<PERSON>", "url": "github.com/kevva"}, "scripts": {"test": "xo && ava"}, "engines": {"node": ">=4"}, "files": ["index.js"], "keywords": ["http", "https", "proxy", "tunnel"], "dependencies": {"get-proxy": "^2.0.0", "isurl": "^1.0.0-alpha5", "tunnel-agent": "^0.6.0", "url-to-options": "^1.0.1"}, "devDependencies": {"ava": "*", "create-cert": "^1.0.4", "get-port": "^3.1.0", "got": "^7.0.0", "pify": "^3.0.0", "proxyquire": "^1.7.9", "sinon": "^2.3.1", "universal-url": "1.0.0-alpha", "xo": "*"}, "xo": {"rules": {"ava/no-skip-test": 0}}}