{"name": "got", "version": "6.7.1", "description": "Simplified HTTP requests", "license": "MIT", "repository": "sindresorhus/got", "maintainers": [{"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "sindresorhus.com"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>", "url": "github.com/floatdrop"}], "engines": {"node": ">=4"}, "browser": {"unzip-response": false}, "scripts": {"test": "xo && nyc ava", "coveralls": "nyc report --reporter=text-lcov | coveralls"}, "files": ["index.js"], "keywords": ["http", "https", "get", "got", "url", "uri", "request", "util", "utility", "simple", "curl", "wget", "fetch"], "dependencies": {"create-error-class": "^3.0.0", "duplexer3": "^0.1.4", "get-stream": "^3.0.0", "is-redirect": "^1.0.0", "is-retry-allowed": "^1.0.0", "is-stream": "^1.0.0", "lowercase-keys": "^1.0.0", "safe-buffer": "^5.0.1", "timed-out": "^4.0.0", "unzip-response": "^2.0.1", "url-parse-lax": "^1.0.0"}, "devDependencies": {"ava": "^0.17.0", "coveralls": "^2.11.4", "form-data": "^2.1.1", "get-port": "^2.0.0", "into-stream": "^3.0.0", "nyc": "^10.0.0", "pem": "^1.4.4", "pify": "^2.3.0", "tempfile": "^1.1.1", "xo": "*"}, "xo": {"esnext": true}, "ava": {"concurrency": 4}}