{"name": "is-redirect", "version": "1.0.0", "description": "Check if a number is a redirect HTTP status code", "license": "MIT", "repository": "sindresorhus/is-redirect", "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "sindresorhus.com"}, "engines": {"node": ">=0.10.0"}, "scripts": {"test": "node test.js"}, "files": ["index.js"], "keywords": ["redirect", "http", "https", "status", "code", "codes", "is", "check", "detect"], "devDependencies": {"ava": "0.0.4"}}