{"author": "<PERSON> <<EMAIL>> (http://blog.izs.me/)", "name": "ini", "description": "An ini encoder/decoder for node", "version": "1.3.5", "repository": {"type": "git", "url": "git://github.com/isaacs/ini.git"}, "main": "ini.js", "scripts": {"pretest": "standard ini.js", "test": "tap test/*.js --100 -J", "preversion": "npm test", "postversion": "npm publish", "postpublish": "git push origin --all; git push origin --tags"}, "engines": {"node": "*"}, "dependencies": {}, "devDependencies": {"standard": "^10.0.3", "tap": "^10.7.3 || 11"}, "license": "ISC", "files": ["ini.js"]}