{"name": "log-symbols", "version": "2.2.0", "description": "Colored symbols for various log levels. Example: ✔︎ Success", "license": "MIT", "repository": "sindresorhus/log-symbols", "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "sindresorhus.com"}, "engines": {"node": ">=4"}, "scripts": {"test": "xo && ava"}, "files": ["index.js", "browser.js"], "keywords": ["unicode", "cli", "cmd", "command-line", "characters", "char", "symbol", "symbols", "figure", "figures", "fallback", "win", "windows", "log", "logging", "terminal", "stdout"], "dependencies": {"chalk": "^2.0.1"}, "devDependencies": {"ava": "*", "strip-ansi": "^4.0.0", "xo": "*"}, "browser": "browser.js"}