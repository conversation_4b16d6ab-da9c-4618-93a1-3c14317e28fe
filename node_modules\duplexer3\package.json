{"name": "duplexer3", "version": "0.1.4", "description": "Like duplexer but using streams3", "engine": {"node": ">=4"}, "files": ["index.js"], "scripts": {"test": "mocha -R tap"}, "repository": "floatdrop/duplexer3", "keywords": ["duplex", "duplexer", "stream", "stream3", "join", "combine"], "author": "<PERSON> <<EMAIL>> (http://www.fknsrs.biz/)", "license": "BSD-3-<PERSON><PERSON>", "devDependencies": {"mocha": "^2.2.5"}}