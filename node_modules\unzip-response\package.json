{"name": "unzip-response", "version": "2.0.1", "description": "Unzip a HTTP response if needed", "license": "MIT", "repository": "sindresorhus/unzip-response", "maintainers": [{"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "sindresorhus.com"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>", "url": "github.com/floatdrop"}], "engines": {"node": ">=4"}, "scripts": {"test": "xo && ava"}, "files": ["index.js"], "keywords": ["http", "unzip", "zlib", "gzip", "deflate", "incoming", "message", "response", "stream"], "devDependencies": {"ava": "*", "get-stream": "^2.3.0", "pify": "^2.3.0", "rfpify": "^1.0.0", "xo": "*"}, "xo": {"esnext": true}}