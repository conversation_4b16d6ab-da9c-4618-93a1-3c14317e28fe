{"name": "make-dir", "version": "1.3.0", "description": "Make a directory and its parents if needed - Think `mkdir -p`", "license": "MIT", "repository": "sindresorhus/make-dir", "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "sindresorhus.com"}, "engines": {"node": ">=4"}, "scripts": {"test": "xo && nyc ava"}, "files": ["index.js"], "keywords": ["mkdir", "mkdirp", "make", "directories", "dir", "dirs", "folders", "directory", "folder", "path", "parent", "parents", "intermediate", "recursively", "recursive", "create", "fs", "filesystem", "file-system"], "dependencies": {"pify": "^3.0.0"}, "devDependencies": {"ava": "*", "codecov": "^3.0.0", "graceful-fs": "^4.1.11", "nyc": "^11.3.0", "path-type": "^3.0.0", "tempy": "^0.2.1", "xo": "^0.20.0"}}