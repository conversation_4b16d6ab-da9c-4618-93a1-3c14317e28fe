{"name": "download-git-repo", "version": "1.1.0", "license": "MIT", "repository": "git://github.com/flipxfx/download-git-repo", "description": "Download and extract a git repository (GitHub, GitLab, Bitbucket) from node.", "keywords": ["download", "github", "bitbucket", "repo", "repository", "tar", "extract", "tarball"], "dependencies": {"download": "^5.0.3", "git-clone": "^0.1.0", "rimraf": "^2.6.1"}, "devDependencies": {"mocha": "^3.2.0", "fs-readdir-recursive": "1.0.0"}, "scripts": {"test": "mocha"}}