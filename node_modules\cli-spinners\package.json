{"name": "cli-spinners", "version": "1.3.1", "description": "Spinners for use in the terminal", "license": "MIT", "repository": "sindresorhus/cli-spinners", "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "sindresorhus.com"}, "engines": {"node": ">=4"}, "scripts": {"test": "xo && ava", "asciicast": "asciinema rec --command='node example-all.js' --title='cli-spinner' --quiet"}, "files": ["index.js", "spinners.json"], "keywords": ["cli", "spinner", "spinners", "terminal", "term", "console", "ascii", "unicode", "loading", "indicator", "progress", "busy", "wait", "idle", "json"], "devDependencies": {"ava": "*", "log-update": "^2.1.0", "xo": "*"}}